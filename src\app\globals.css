@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #000000;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, sans-serif;
}

/* Ensure all text is black and backgrounds are white */
h1, h2, h3, h4, h5, h6, p, li, a {
  color: var(--foreground);
}

/* Clean form elements */
input, textarea {
  background: var(--background);
  color: var(--foreground);
  outline: none;
}

input:focus, textarea:focus {
  border-color: #000;
  ring: 1px solid #000;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff; /* Keep white background even in dark mode */
    --foreground: #000000; /* Keep black text even in dark mode */
  }
}
