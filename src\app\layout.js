import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "<PERSON> | Fullstack Developer & UI/UX Enthusiast",
  description: "Passionate fullstack developer with 5+ years of experience building modern web applications. Specializing in React, Next.js, Node.js, and creating exceptional user experiences.",
  keywords: ["fullstack developer", "web developer", "react", "nextjs", "nodejs", "javascript", "typescript", "portfolio"],
  authors: [{ name: "<PERSON>" }],
  creator: "<PERSON>",
  publisher: "<PERSON>",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://johndoe.dev"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "<PERSON> | Fullstack Developer & UI/UX Enthusiast",
    description: "Passionate fullstack developer with 5+ years of experience building modern web applications.",
    url: "https://johndoe.dev",
    siteName: "<PERSON> Doe Portfolio",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "John Doe - Fullstack Developer",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "John Doe | Fullstack Developer & UI/UX Enthusiast",
    description: "Passionate fullstack developer with 5+ years of experience building modern web applications.",
    images: ["/og-image.jpg"],
    creator: "@johndoe",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: "/favicon.svg",
    shortcut: "/favicon.svg",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/site.webmanifest",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
