import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header/Navigation */}
      <header className="py-6 px-8 md:px-16 flex justify-between items-center border-b border-gray-100">
        <h1 className="text-xl font-medium"><PERSON></h1>
        <nav>
          <ul className="flex space-x-6">
            <li><a href="#about" className="hover:underline">About</a></li>
            <li><a href="#projects" className="hover:underline">Projects</a></li>
            <li><a href="#skills" className="hover:underline">Skills</a></li>
            <li><a href="#contact" className="hover:underline">Contact</a></li>
          </ul>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-8 md:px-16 flex flex-col items-center text-center md:text-left md:items-start max-w-4xl mx-auto">
        <h2 className="text-4xl md:text-5xl font-bold mb-6">Fullstack Developer</h2>
        <p className="text-lg mb-8 max-w-2xl">I build elegant, responsive, and performant web applications with modern technologies.</p>
        <div className="flex space-x-4">
          <a href="#contact" className="bg-black text-white px-6 py-3 rounded-md hover:bg-gray-800 transition-colors">
            Get in touch
          </a>
          <a href="#projects" className="border border-black px-6 py-3 rounded-md hover:bg-gray-50 transition-colors">
            View my work
          </a>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 px-8 md:px-16 max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold mb-8 border-b pb-2">About Me</h2>
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <p className="mb-4">
              Hello! I'm a passionate fullstack developer with expertise in building modern web applications. I enjoy solving complex problems and creating intuitive user experiences.
            </p>
            <p className="mb-4">
              With a strong foundation in both frontend and backend technologies, I bring ideas to life with clean, maintainable code and thoughtful architecture.
            </p>
            <p>
              When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or enjoying outdoor activities.
            </p>
          </div>
          <div className="bg-gray-100 h-64 flex items-center justify-center">
            <p className="text-gray-500">Your photo here</p>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-20 px-8 md:px-16 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-8 border-b pb-2">Projects</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {/* Project 1 */}
            <div className="bg-white p-6 rounded-md shadow-sm">
              <div className="bg-gray-100 h-48 mb-4 flex items-center justify-center">
                <p className="text-gray-500">Project image</p>
              </div>
              <h3 className="text-xl font-bold mb-2">Project One</h3>
              <p className="mb-4">A brief description of the project, its purpose, and the technologies used.</p>
              <div className="flex space-x-4">
                <a href="#" className="text-sm hover:underline">View Project</a>
                <a href="#" className="text-sm hover:underline">GitHub</a>
              </div>
            </div>
            
            {/* Project 2 */}
            <div className="bg-white p-6 rounded-md shadow-sm">
              <div className="bg-gray-100 h-48 mb-4 flex items-center justify-center">
                <p className="text-gray-500">Project image</p>
              </div>
              <h3 className="text-xl font-bold mb-2">Project Two</h3>
              <p className="mb-4">A brief description of the project, its purpose, and the technologies used.</p>
              <div className="flex space-x-4">
                <a href="#" className="text-sm hover:underline">View Project</a>
                <a href="#" className="text-sm hover:underline">GitHub</a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="py-20 px-8 md:px-16 max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold mb-8 border-b pb-2">Skills</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
          <div>
            <h3 className="text-xl font-medium mb-4">Frontend</h3>
            <ul className="space-y-2">
              <li>HTML/CSS</li>
              <li>JavaScript/TypeScript</li>
              <li>React</li>
              <li>Next.js</li>
              <li>Tailwind CSS</li>
            </ul>
          </div>
          <div>
            <h3 className="text-xl font-medium mb-4">Backend</h3>
            <ul className="space-y-2">
              <li>Node.js</li>
              <li>Express</li>
              <li>Python</li>
              <li>Django</li>
              <li>RESTful APIs</li>
            </ul>
          </div>
          <div>
            <h3 className="text-xl font-medium mb-4">Other</h3>
            <ul className="space-y-2">
              <li>Git/GitHub</li>
              <li>MongoDB</li>
              <li>PostgreSQL</li>
              <li>Docker</li>
              <li>AWS</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-8 md:px-16 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-8 border-b pb-2">Contact</h2>
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <p className="mb-6">I'm always open to new opportunities and collaborations. Feel free to reach out!</p>
              <div className="space-y-4">
                <p>
                  <span className="font-medium">Email:</span> <a href="mailto:<EMAIL>" className="hover:underline"><EMAIL></a>
                </p>
                <p>
                  <span className="font-medium">Location:</span> New York, NY
                </p>
                <div className="flex space-x-4 mt-6">
                  <a href="#" className="hover:text-gray-600">GitHub</a>
                  <a href="#" className="hover:text-gray-600">LinkedIn</a>
                  <a href="#" className="hover:text-gray-600">Twitter</a>
                </div>
              </div>
            </div>
            <form className="space-y-4">
              <div>
                <label htmlFor="name" className="block mb-1">Name</label>
                <input type="text" id="name" className="w-full p-2 border border-gray-300 rounded-md" />
              </div>
              <div>
                <label htmlFor="email" className="block mb-1">Email</label>
                <input type="email" id="email" className="w-full p-2 border border-gray-300 rounded-md" />
              </div>
              <div>
                <label htmlFor="message" className="block mb-1">Message</label>
                <textarea id="message" rows="4" className="w-full p-2 border border-gray-300 rounded-md"></textarea>
              </div>
              <button type="submit" className="bg-black text-white px-6 py-3 rounded-md hover:bg-gray-800 transition-colors">
                Send Message
              </button>
            </form>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 px-8 md:px-16 border-t border-gray-100 text-center">
        <p>© {new Date().getFullYear()} John Doe. All rights reserved.</p>
      </footer>
    </div>
  );
}
